import { expect, test } from '@playwright/test'
import { TestUtils } from '../helpers/tests/test-utils'

test.describe('EKO-132: Right Click Context Menu', () => {
  let testUtils: TestUtils
  
  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    
    // Login first
    await testUtils.login()
    
    // Create a new document with the editor
    await testUtils.createDocumentFromTemplate()
    
    // Wait for the editor to be available
    await page.waitForSelector('[data-testid="eko-document-editor"]', { timeout: 10000 })
    await page.waitForSelector('.ProseMirror', { timeout: 5000 })
  })

  test('should show context menu on right click in editor', async ({ page }) => {
    const editor = await testUtils.waitForEditor()
    
    // Right click in the editor
    await editor.click({ button: 'right' })
    
    // Context menu should be visible
    await expect(page.locator('[data-testid="right-click-context-menu"]')).toBeVisible()
    
    // Should have basic menu items
    await expect(page.locator('[data-testid="context-menu-selectAll"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-insert"]')).toBeVisible()
  })

  test('should show clipboard operations when text is selected', async ({ page }) => {
    const editor = await testUtils.waitForEditor()
    
    // Type some text
    await editor.fill('Test text for clipboard operations')
    
    // Select all text
    await page.keyboard.press('ControlOrMeta+a')
    
    // Right click on selected text
    await editor.click({ button: 'right' })
    
    // Context menu should be visible
    await expect(page.locator('[data-testid="right-click-context-menu"]')).toBeVisible()
    
    // Should show clipboard operations
    await expect(page.locator('[data-testid="context-menu-copy"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-cut"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-paste"]')).toBeVisible()
  })

  test('should show formatting options for selected text', async ({ page }) => {
    const editor = await testUtils.waitForEditor()
    
    // Type and select text
    await editor.fill('Test formatting text')
    await page.keyboard.press('ControlOrMeta+a')
    
    // Right click
    await editor.click({ button: 'right' })
    
    // Context menu should be visible
    await expect(page.locator('[data-testid="right-click-context-menu"]')).toBeVisible()
    
    // Should show format submenu (using correct ID: formatting)
    await expect(page.locator('[data-testid="context-menu-formatting"]')).toBeVisible()
    
    // Hover over Format to show submenu
    await page.locator('[data-testid="context-menu-formatting"]').hover()
    
    // Should show formatting options in submenu
    await expect(page.locator('[data-testid="context-submenu-formatting"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-heading1"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-heading2"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-bulletList"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-orderedList"]')).toBeVisible()
  })

  test('should show comprehensive Insert submenu', async ({ page }) => {
    const editor = await testUtils.waitForEditor()
    
    // Right click in editor
    await editor.click({ button: 'right' })
    
    // Context menu should be visible
    await expect(page.locator('[data-testid="right-click-context-menu"]')).toBeVisible()
    
    // Hover over Insert menu to show submenu
    await page.locator('[data-testid="context-menu-insert"]').hover()
    
    // Should show insert submenu
    await expect(page.locator('[data-testid="context-submenu-insert"]')).toBeVisible()
    
    // Should show report-specific items (using correct IDs)
    await expect(page.locator('[data-testid="context-menu-insertTOC"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-insertReportSummary"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-insertReportSection"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-insertReportGroup"]')).toBeVisible()
    
    // Should show multi-columns with submenu
    await expect(page.locator('[data-testid="context-menu-insertColumns"]')).toBeVisible()
    
    // Should show basic content types
    await expect(page.locator('[data-testid="context-menu-insertTable"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-insertChart"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-insertDetails"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-insertCodeBlock"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-insertBlockquote"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-insertMath"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-insertHorizontalRule"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-insertImage"]')).toBeVisible()
  })

  test('should show multi-column layout submenu', async ({ page }) => {
    const editor = await testUtils.waitForEditor()
    
    // Right click and navigate to Multi-Columns
    await editor.click({ button: 'right' })
    
    // Context menu should be visible
    await expect(page.locator('[data-testid="right-click-context-menu"]')).toBeVisible()
    
    // Hover over Insert to show submenu
    await page.locator('[data-testid="context-menu-insert"]').hover()
    await expect(page.locator('[data-testid="context-submenu-insert"]')).toBeVisible()
    
    // Hover over Multi-Columns to show nested submenu
    await page.locator('[data-testid="context-menu-insertColumns"]').hover()
    
    // Should show column layout options (using correct IDs)
    await expect(page.locator('[data-testid="context-submenu-insertColumns"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-columns2Equal"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-columns3Equal"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-columns4Equal"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-columns1-2"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-columns2-1"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-columnsCentered"]')).toBeVisible()
  })

  test('should insert table when selected from context menu', async ({ page }) => {
    const editor = await testUtils.waitForEditor()
    
    // Right click and insert table
    await editor.click({ button: 'right' })
    await expect(page.locator('[data-testid="right-click-context-menu"]')).toBeVisible()
    
    // Navigate to Insert submenu
    await page.locator('[data-testid="context-menu-insert"]').hover()
    await expect(page.locator('[data-testid="context-submenu-insert"]')).toBeVisible()
    
    // Click table option
    await page.locator('[data-testid="context-menu-insertTable"]').click()
    
    // Should have inserted a table
    await expect(editor.locator('table')).toBeVisible()
    await expect(editor.locator('tr')).toHaveCount(3) // 3 rows by default
    await expect(editor.locator('td, th')).toHaveCount(9) // 3x3 = 9 cells
  })

  test('should insert report section when selected from context menu', async ({ page }) => {
    const editor = await testUtils.waitForEditor()
    
    // Right click and insert report section
    await editor.click({ button: 'right' })
    await expect(page.locator('[data-testid="right-click-context-menu"]')).toBeVisible()
    
    // Navigate to Insert submenu
    await page.locator('[data-testid="context-menu-insert"]').hover()
    await expect(page.locator('[data-testid="context-submenu-insert"]')).toBeVisible()
    
    // Click report section option
    await page.locator('[data-testid="context-menu-insertReportSection"]').click()
    
    // Should have inserted a report section component
    await expect(editor.locator('[data-type="reportSection"]')).toBeVisible()
  })

  test('should insert chart when selected from context menu', async ({ page }) => {
    const editor = await testUtils.waitForEditor()
    
    // Right click and insert chart
    await editor.click({ button: 'right' })
    await expect(page.locator('[data-testid="right-click-context-menu"]')).toBeVisible()
    
    // Navigate to Insert submenu
    await page.locator('[data-testid="context-menu-insert"]').hover()
    await expect(page.locator('[data-testid="context-submenu-insert"]')).toBeVisible()
    
    // Click chart option
    await page.locator('[data-testid="context-menu-insertChart"]').click()
    
    // Should have inserted a chart component
    await expect(editor.locator('[data-type="chart"]')).toBeVisible()
  })

  test('should insert 2-column layout when selected from context menu', async ({ page }) => {
    const editor = await testUtils.waitForEditor()
    
    // Right click and navigate to columns
    await editor.click({ button: 'right' })
    await expect(page.locator('[data-testid="right-click-context-menu"]')).toBeVisible()
    
    // Navigate to Insert submenu
    await page.locator('[data-testid="context-menu-insert"]').hover()
    await expect(page.locator('[data-testid="context-submenu-insert"]')).toBeVisible()
    
    // Navigate to Multi-Columns submenu
    await page.locator('[data-testid="context-menu-insertColumns"]').hover()
    await expect(page.locator('[data-testid="context-submenu-insertColumns"]')).toBeVisible()
    
    // Click 2 columns equal option
    await page.locator('[data-testid="context-menu-columns2Equal"]').click()
    
    // Should have inserted columns
    await expect(editor.locator('[data-type="columns"]')).toBeVisible()
    await expect(editor.locator('[data-type="column"]')).toHaveCount(2)
  })

  test('should show Add Comment option when text is selected and onAddComment is provided', async ({ page }) => {
    const editor = await testUtils.waitForEditor()
    
    // Type and select text
    await editor.fill('Text to comment on')
    await page.keyboard.press('ControlOrMeta+a')
    
    // Right click
    await editor.click({ button: 'right' })
    
    // Context menu should be visible
    await expect(page.locator('[data-testid="right-click-context-menu"]')).toBeVisible()
    
    // Should show Add Comment option
    await expect(page.locator('[data-testid="context-menu-addComment"]')).toBeVisible()
  })

  test('should add link when selected from context menu', async ({ page }) => {
    const editor = await testUtils.waitForEditor()
    
    // Type and select text
    await editor.fill('Link text')
    await page.keyboard.press('ControlOrMeta+a')
    
    // Right click and add link
    await editor.click({ button: 'right' })
    await expect(page.locator('[data-testid="right-click-context-menu"]')).toBeVisible()
    
    // Click add link option
    await page.locator('[data-testid="context-menu-addLink"]').click()
    
    // Handle the prompt for URL input
    page.on('dialog', async dialog => {
      expect(dialog.message()).toContain('Enter URL')
      await dialog.accept('https://example.com')
    })
    
    // Should have created a link
    await expect(editor.locator('a[href="https://example.com"]')).toBeVisible()
  })

  test('should apply heading formatting when selected from context menu', async ({ page }) => {
    const editor = await testUtils.waitForEditor()
    
    // Type and select text
    await editor.fill('Heading text')
    await page.keyboard.press('ControlOrMeta+a')
    
    // Right click and format as heading
    await editor.click({ button: 'right' })
    await expect(page.locator('[data-testid="right-click-context-menu"]')).toBeVisible()
    
    // Navigate to Format submenu
    await page.locator('[data-testid="context-menu-formatting"]').hover()
    await expect(page.locator('[data-testid="context-submenu-formatting"]')).toBeVisible()
    
    // Click Heading 1 option
    await page.locator('[data-testid="context-menu-heading1"]').click()
    
    // Should have applied heading formatting
    await expect(editor.locator('h1')).toHaveText('Heading text')
  })

  test('should close context menu when clicking elsewhere', async ({ page }) => {
    const editor = await testUtils.waitForEditor()
    
    // Right click to show menu
    await editor.click({ button: 'right' })
    await expect(page.locator('[data-testid="right-click-context-menu"]')).toBeVisible()
    
    // Click elsewhere to close
    await page.click('body', { position: { x: 100, y: 100 } })
    
    // Menu should be hidden
    await expect(page.locator('[data-testid="right-click-context-menu"]')).not.toBeVisible()
  })

  test('should close context menu when pressing Escape', async ({ page }) => {
    const editor = await testUtils.waitForEditor()
    
    // Right click to show menu
    await editor.click({ button: 'right' })
    await expect(page.locator('[data-testid="right-click-context-menu"]')).toBeVisible()
    
    // Press Escape to close
    await page.keyboard.press('Escape')
    
    // Menu should be hidden
    await expect(page.locator('[data-testid="right-click-context-menu"]')).not.toBeVisible()
  })
})
