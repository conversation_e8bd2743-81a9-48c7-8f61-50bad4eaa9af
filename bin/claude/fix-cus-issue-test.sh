#!/bin/bash
set -eux
cd  $(dirname $0)/../.. || exit
src_dir="$(pwd)"
#Create if not exists
if [ ! -d ~/claude_workdirs/issue-test-$1/repo ]; then
  mkdir -p ~/claude_workdirs/issue-test-$1/repo
  git clone "**************:ekointelligence/mono-repo.git" ~/claude_workdirs/issue-test-$1/repo
  cd ~/claude_workdirs/issue-test-$1/repo
  git checkout -b feature/test-$1 || true
  git branch --set-upstream-to=origin/main feature/test-$1
  cd -
fi
#git worktree add -B feature/eko-$1 ~/claude_worktrees/$1 origin/main
cd $src_dir
cp .mcp.json ~/claude_workdirs/issue-test-$1/repo/.mcp.json
mkdir -p ~/claude_workdirs/issue-test-$1/repo/.claude
cp -f .claude/settings.json ~/claude_workdirs/issue-test-$1/repo/.claude/
cp -f .claude/settings.json ~/claude_workdirs/issue-test-$1/repo/.claude/settings.local.json
cp  apps/customer/.env.development.local ~/claude_workdirs/issue-test-$1/repo/apps/customer/.env.development.local
cd  ~/claude_workdirs/issue-test-$1/repo
id=$1
cd ..
"$src_dir"/bin/claude-docker  issue-test-$id  --model=sonnet --dangerously-skip-permissions  "

Please read apps/customer/tests/CLUADE.md for details on writing tests.

Please fix the test apps/customer/tests/issues/issue-eko-$1.spec.ts

- Run playwright tests as follows:  \`cd apps/customer && npx playwright test --reporter=line tests/issues/issue-eko-$1.spec.ts\` .
- If stuck get more logging with  \`DEBUG=pw:* cd apps/customer && npx playwright test --reporter=line tests/issues/issue-eko-$1.spec.ts\`

Firstly look at a working test such as apps/customer/tests/editor-ai-features.spec.ts or apps/customer/tests/document-templates.spec.ts and see how it works.

---
Each test should follow a clear sequential path from start to finish, it should not branch, it should not have try/catch, it should not fallback it should ignore failure states. A test either passes or fails completely, it cannot partially pass.
---

You're doing a great job, keep at it, plan this out and ultra think carefully step by and work your way through this methodically. Be your best self!
"

RECHECK="Thanks for the help. Please check  apps/customer/tests/issues/issue-eko-$1.spec.ts against guidelines in apps/customer/tests/CLAUDE.md, and ensure each test should follow a clear sequential path from start to finish, it should not branch, it should not have try/catch, it should not fallback it should ignore failure states. A test either passes or fails completely, it cannot partially pass."
"$src_dir"/bin/claude-docker  issue-test-$id  --model=sonnet --dangerously-skip-permissions -p "$RECHECK"

export PW_PORT=${2:-3333}
# Loop while tests are failing
count=0
mkdir -p ~/claude_workdirs/issue-test-$1/test-results/${count}/
until (cd ~/claude_workdirs/issue-test-$1/repo/apps/customer/ && npx playwright test --output=~/claude_workdirs/issue-test-$1/test-results/${count}/ tests/issues/issue-eko-"$id".spec.ts) > .last-test-run || [ $count -eq 3 ]
do
  cd ~/claude_workdirs/issue-test-$1
  cat .last-test-run
  "$src_dir"/bin/claude-docker  issue-test-$id  --model=sonnet --dangerously-skip-permissions -p "The tests are still failing in @apps/customer/tests/issues/issue-eko-$id.spec.ts, please fix this. The log file is in .last-test-run. The test result files are in /workspace/test-results/${count}/ Please fix these failing tests. Do not add try/catch or if/else blocks to bypass the testing."
  "$src_dir"/bin/claude-docker  issue-test-$id  --model=sonnet --dangerously-skip-permissions -p "$RECHECK"
  ((count++))
done

if (( count < 3 ))
then
  cd  ~/claude_workdirs/issue-test-$1
  "$src_dir"/bin/claude-docker  issue-test-$id  --model=sonnet --dangerously-skip-permissions -p "Thanks for the help. Please commit  feature/issue-test-$id and push all changes, then create a PR."
fi

if (( count == 3 ))
then
  cd  ~/claude_workdirs/issue-test-$1
  "$src_dir"/bin/claude-docker  issue-test-$id  --model=sonnet --dangerously-skip-permissions  -p "The tests are still failing in @apps/customer/tests/issues/issue-eko-$id.spec.ts, please fix this. The log file is in .last-test-run. The test result files are in /workspace/test-results/${count}/ Please fix these failing tests. Do not add try/catch or if/else blocks to bypass the testing."
fi
